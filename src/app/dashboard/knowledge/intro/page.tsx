'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { createClientComponentClient } from '@/utils/supabase/client'
import { useDashboardData } from '@/hooks/useOptimizedData'
import { FaBrain, FaImage, FaComments, FaSignOutAlt, FaExclamationTriangle } from 'react-icons/fa'
import { v4 as uuidv4 } from 'uuid'
import { sendWelcomeUpdateWebhook } from '@/app/api/webhooks/welcome'
import { useLanguage } from '@/context/LanguageContext'
import { optimizeGalleryImage } from '@/utils/imageOptimization'

// Optimized PhotoThumbnail component for fast loading without Vercel Image
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  return (
    <div
      className={`${className} bg-zinc-700 rounded overflow-hidden flex-shrink-0 relative ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 bg-zinc-600 transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className="w-full h-full flex items-center justify-center bg-white/5 text-zinc-400 text-xs">
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

export default function IntrosOutrosPage() {
  // Use dashboard cache for knowledge stats and client info
  const { data: dashboardData, loading: isDashboardLoading } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo
  const knowledgeStats = dashboardData?.knowledgeStats

  // Get knowledge stats from dashboard cache
  const totalFaqs = knowledgeStats?.faqCount || 0
  const photoCount = knowledgeStats?.photoCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const photoLimit = knowledgeStats?.photoLimit || 0
  const faqUsagePercentage = knowledgeStats?.faqUsagePercentage || 0
  const photoUsagePercentage = knowledgeStats?.photoUsagePercentage || 0
  const isLoadingCount = isDashboardLoading

  // Create Supabase client
  const supabase = createClientComponentClient()
  const { t } = useLanguage()

  // Photo search state - for intro only
  const [introSearchQuery, setIntroSearchQuery] = useState('')
  const [introSearchResults, setIntroSearchResults] = useState<any[]>([])
  const [showIntroResults, setShowIntroResults] = useState(false)
  const [isSearchingIntro, setIsSearchingIntro] = useState(false)
  const [selectedIntroPhoto, setSelectedIntroPhoto] = useState<any>(null)

  // Loading animation states for photo selection
  const [isIntroPhotoLoading, setIsIntroPhotoLoading] = useState(false)
  const introSearchResultsRef = useRef<HTMLDivElement>(null)
  const introSearchInputRef = useRef<HTMLInputElement>(null)

  // Add state for all photos
  const [allPhotos, setAllPhotos] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])

  // Image gallery state
  const [imageGallery, setImageGallery] = useState<{urls: string[], currentIndex: number} | null>(null)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const imageGalleryRef = useRef<HTMLDivElement>(null)

  // Audio recording state
  const [isRecording, setIsRecording] = useState(false)
  const [recordingFor, _setRecordingFor] = useState<'intro' | null>(null)

  // Custom setter for recordingFor to track changes
  const setRecordingFor = (value: 'intro' | null) => {
    // Don't clear any audio UI when starting recording
    _setRecordingFor(value);
  }
  const [recordingTime, setRecordingTime] = useState(0)
  const [introAudioUrl, setIntroAudioUrl] = useState<string | null>(null)
  const [recordingError, setRecordingError] = useState<string | null>(null)
  const [showRecordingPopup, setShowRecordingPopup] = useState(false)

  // Add new state for showing audio error popup
  const [showAudioErrorPopup, setShowAudioErrorPopup] = useState(false)
  // Add state to track which section has the error
  const [audioErrorSection, setAudioErrorSection] = useState<'intro' | null>(null)
  // Add state to track if a recording is over 60 seconds
  const [isRecordingTooLong, setIsRecordingTooLong] = useState(false)

  // CRITICAL FIX: Add state to track if a recording was too long for intro section
  const [introRecordingTooLong, setIntroRecordingTooLong] = useState(false)

  // Separate recording state indicators for intro and outro
  // Commented out since we now have the animation recording in the window popup
  // const [isIntroRecording, setIsIntroRecording] = useState(false)
  // const [isOutroRecording, setIsOutroRecording] = useState(false)

  // Enhanced recording state
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null)
  const [audioRecorder, setAudioRecorder] = useState<MediaRecorder | null>(null)
  const [audioChunks, setAudioChunks] = useState<Blob[]>([])
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null)
  const [audioInitialized, setAudioInitialized] = useState(false)

  // Refs for timers and audio
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  // Audio playback state
  const [isPlaying, setIsPlaying] = useState<'intro' | null>(null)
  const [audioPlayer, setAudioPlayer] = useState<HTMLAudioElement | null>(null)
  const [playbackProgress, setPlaybackProgress] = useState(0)
  const [playbackTime, setPlaybackTime] = useState(0)
  const [audioDuration, setAudioDuration] = useState(0)
  const [introAudioDuration, setIntroAudioDuration] = useState(0)

  // Text content state
  const [introText, _setIntroText] = useState('')

  // Custom setters to track changes
  const setIntroText = (value: string) => {
    _setIntroText(value);
  }

  const [isIntroSaving, setIsIntroSaving] = useState(false)

  // Audio field saving indicators (separate from the save button)
  const [isIntroAudioSaving, setIsIntroAudioSaving] = useState(false)

  const [isUpdating, setIsUpdating] = useState(false)

  // Edit mode state
  const [isIntroEditing, setIsIntroEditing] = useState(false)
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false)
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false)
  const [confirmationSection, setConfirmationSection] = useState<'intro' | null>(null)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateProgress, setUpdateProgress] = useState(0)
  const [updateMessage, setUpdateMessage] = useState('')

  // Initial state tracking for detecting changes
  const [initialIntroText, setInitialIntroText] = useState('')
  const [initialIntroAudioUrl, setInitialIntroAudioUrl] = useState<string | null>(null)
  const [initialIntroAudioDuration, setInitialIntroAudioDuration] = useState(0)
  const [initialIntroAudioFilePath, setInitialIntroAudioFilePath] = useState<string | null>(null)
  const [initialSelectedIntroPhoto, setInitialSelectedIntroPhoto] = useState<any>(null)

  // Store actual chat_ids from database
  const [introChatId, setIntroChatId] = useState<string | null>(null)
  const [hasIntroChanges, setHasIntroChanges] = useState(false)

  // Audio deletion confirmation
  const [showDeleteAudioConfirmation, setShowDeleteAudioConfirmation] = useState(false)
  const [audioToDelete, setAudioToDelete] = useState<'intro' | null>(null)

  // Editing state
  const [editingItem, setEditingItem] = useState<{
    section: 'intro' | 'outro' | null;
    value: string;
  } | null>(null)

  // Track if textarea has been focused for mobile behavior
  const [hasFocusedInput, setHasFocusedInput] = useState(false)

  // Ref for textarea auto-focus
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const modalRef = useRef<HTMLDivElement>(null)

  // Removed manual fetch functions - now using dashboard cache

  // Format recording time
  const formatRecordingTime = (seconds: number) => {
    // During recording, just return the seconds
    return `${seconds}s`;
  };

  // Handle intro photo search
  const handleIntroPhotoSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setIntroSearchQuery(query);
    searchIntroPhotos(query);
  };



  // Fetch all photos from Supabase
  const fetchAllPhotos = async () => {
    try {
      // Wait for dashboard data to load before fetching photos
      if (!clientInfo?.client_id) {
        return
      }

      const clientId = clientInfo.client_id

      const { data, error } = await supabase
        .from('photos')
        .select('id, photo_id, photo_url, photo_file_path')
        .eq('client_id', clientId)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching photos:', error);
        return;
      }

      setAllPhotos(data || []);
    } catch (error) {
      console.error('Error in fetchAllPhotos:', error);
    }
  };

  // Search photos for intro section
  const searchIntroPhotos = async (query: string) => {
    if (!query.trim()) {
      setIntroSearchResults([]);
      setShowIntroResults(false);
      return;
    }

    setIsSearchingIntro(true);
    try {
      // First try to search locally if we have photos cached
      if (allPhotos.length > 0) {
        const filteredPhotos = allPhotos.filter(photo =>
          photo.photo_id.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5);

        if (filteredPhotos.length > 0) {
          setIntroSearchResults(filteredPhotos);
          setShowIntroResults(true);
          setIsSearchingIntro(false);
          return;
        }
      }

      // If no local results or no local data, fetch from server
      const clientId = clientInfo?.client_id;

      if (!clientId) {
        console.error('Client ID not found while searching photos');
        return;
      }

      // Search photos that match the query by photo_id
      const { data, error } = await supabase
        .from('photos')
        .select('id, photo_id, photo_url, photo_file_path')
        .eq('client_id', clientId)
        .ilike('photo_id', `%${query}%`)
        .limit(5);

      if (error) {
        console.error('Error searching photos:', error);
        return;
      }

      setIntroSearchResults(data || []);
      setShowIntroResults(data && data.length > 0);
    } catch (error) {
      console.error('Error in searchIntroPhotos:', error);
    } finally {
      setIsSearchingIntro(false);
    }
  };



  // Handle selecting a photo from intro search results
  const handleSelectIntroPhoto = (photo: {
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }) => {
    // Get first photo URL as thumbnail for display, but store the full array
    const thumbnail = photo.photo_url && photo.photo_url.length > 0 ? photo.photo_url[0] : null;

    const processedPhoto = {
      id: photo.id,
      photo_id: photo.photo_id,
      photo_url: thumbnail,
      full_photo_urls: photo.photo_url // Store the complete array of photo URLs
    };

    // Show loading animation
    setIsIntroPhotoLoading(true);

    // Hide dropdown and clear search query immediately
    setShowIntroResults(false);
    setIntroSearchQuery(''); // Clear the search query after selection

    // Add a small delay to show the loading animation
    setTimeout(() => {
      // Set the selected photo
      setSelectedIntroPhoto(processedPhoto);

      // Update change tracking
      const initialPhotoId = initialSelectedIntroPhoto?.photo_id;
      setHasIntroChanges(
        initialPhotoId !== photo.photo_id ||
        introText !== initialIntroText ||
        (introAudioUrl !== initialIntroAudioUrl)
      );

      // Hide loading animation after a short delay
      setTimeout(() => {
        setIsIntroPhotoLoading(false);
      }, 50);
    }, 100);
  };



  // Clear selected photo
  const handleClearSelectedPhoto = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedIntroPhoto(null);
    setIntroSearchQuery('');

    // Update change tracking
    const hadInitialPhoto = initialSelectedIntroPhoto !== null;
    setHasIntroChanges(
      hadInitialPhoto ||
      introText !== initialIntroText ||
      (introAudioUrl !== initialIntroAudioUrl)
    );
  };

  // View image in gallery
  const handleViewImage = (urls: string[] | null) => {
    if (!urls || urls.length === 0) {
      console.error('No images available to view');
      return;
    }

    // Filter out any invalid URLs
    const validUrls = urls.filter(url => url && url.trim() !== '');

    if (validUrls.length === 0) {
      console.error('No valid images found to preview');
      return;
    }

    // Open the gallery modal
    setImageGallery({
      urls: validUrls,
      currentIndex: 0
    });
  };

  // Navigate to previous image
  const showPreviousImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
    setImageGallery({
      ...imageGallery,
      currentIndex: newIndex
    });
  };

  // Navigate to next image
  const showNextImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
    setImageGallery({
      ...imageGallery,
      currentIndex: newIndex
    });
  };

  // Touch event handlers for swipe navigation
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
    setTouchEnd(null);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd || !imageGallery) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      showNextImage();
    }

    if (isRightSwipe) {
      showPreviousImage();
    }
  };

  // Initialize audio context for better browser compatibility
  const initializeAudio = () => {
    // Check if already initialized or if running on server
    if (typeof window === 'undefined') return;

    try {
      // Create AudioContext after user interaction
      const context = new (window.AudioContext || (window as any).webkitAudioContext)();
      setAudioContext(context);
      setAudioInitialized(true);

      // Optional: Resume context if it's suspended (iOS sometimes starts it suspended)
      if (context.state === 'suspended') {
        context.resume().catch(e => console.error('Error resuming AudioContext:', e));
      }
    } catch (e) {
      console.error('Error initializing AudioContext:', e);
      setRecordingError('Failed to initialize audio. Please ensure your browser supports Web Audio API and permissions are granted.');
    }
  };

  // Start recording audio with enhanced quality
  const startRecording = async () => {

    try {
      // Store current audio URLs to preserve them
      const currentIntroAudioUrl = introAudioUrl;

      // If already recording, stop it first
      if (isRecording) {
        stopRecording();
        // Return early to prevent starting a new recording immediately
        // This allows the UI to reset properly
        return;
      }

      // Make sure we don't lose the audio URLs when starting recording
      if (introAudioUrl !== currentIntroAudioUrl && currentIntroAudioUrl) {
        setIntroAudioUrl(currentIntroAudioUrl);
      }

      // Explicitly reset recording time to 0 and ensure it's updated immediately
      setRecordingTime(0);

      // Clear any previous error states
      setRecordingError(null);
      setAudioErrorSection(null);

      // Reset the recording too long flag
      setIsRecordingTooLong(false);

      // Enhanced audio constraints for better quality
      const audioConstraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000,
          sampleSize: 16,
          channelCount: 1  // Mono for speech clarity
        }
      };

      // Get audio stream with enhanced constraints
      const stream = await navigator.mediaDevices.getUserMedia(audioConstraints);
      setAudioStream(stream);
      // Store the section being recorded without affecting UI state
      setRecordingFor('intro');

      // Prefer high-quality codec options
      const supportedTypes = [
        'audio/mp4;codecs=mp4a.40.2', // AAC encoding
        'audio/mp4',
        'audio/webm;codecs=opus', // Best quality for voice
        'audio/webm',
        'audio/mp3',
        'audio/ogg'
      ];

      let selectedMimeType = '';
      for (const type of supportedTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          selectedMimeType = type;
          break;
        }
      }

      if (!selectedMimeType) {
        console.error("[startRecording] No supported mimeType found for MediaRecorder!");
        alert("Your browser doesn't support audio recording in a compatible format.");
        stream.getTracks().forEach(track => track.stop());
        setAudioStream(null);
        return;
      }

      const chunks: Blob[] = [];
      setAudioChunks([]);

      // Set higher bitrate for better quality
      const recorderOptions = {
        mimeType: selectedMimeType,
        audioBitsPerSecond: 128000  // 128kbps for good voice quality
      };

      const recorder = new MediaRecorder(stream, recorderOptions);
      setAudioRecorder(recorder);

      recorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunks.push(e.data);
        }
      };

      recorder.onstop = () => {
        // Store the current recording section in a local variable to ensure it doesn't change
        // This is crucial because by the time the setTimeout callback runs, recordingFor might be null
        const sectionToUpdate = 'intro';

        // CRITICAL FIX: Use the global variables set in stopRecording to get the actual recording duration
        // This ensures we have the correct duration even if the state has been reset
        const actualRecordingDuration = (window as any)._lastRecordingDuration || 0;
        const actualRecordingSection = (window as any)._lastRecordingSection || sectionToUpdate;

        // CRITICAL FIX: Force a hard-coded check using the actual recording duration from the global variable
        // This ensures we catch recordings over 60 seconds regardless of any state issues
        if (actualRecordingDuration > 60) {

          // Set the recording too long flag to true
          // This will be checked in the setTimeout callback to prevent creating the audio URL
          setIsRecordingTooLong(true);

          // CRITICAL FIX: Also set the section-specific recording too long flag
          // Use both the section from the function parameter and the global variable
          // to ensure we catch all cases
          const actualSection = sectionToUpdate || (window as any)._lastRecordingSection || 'intro';

          setIntroRecordingTooLong(true);
          // Store in a global variable as well to ensure it persists
          (window as any)._introRecordingTooLong = true;

          // Set error state and section
          setRecordingError("Over 60s");
          setAudioErrorSection('intro');

          // Show the error popup
          setShowAudioErrorPopup(true);

          // Stop all audio tracks
          if (audioStream) {
            audioStream.getTracks().forEach(track => track.stop());
            setAudioStream(null);
          }

          // End any saving animations that might have started
          setIsIntroAudioSaving(false);

          // Reset recording state
          setIsRecording(false);
          setRecordingFor(null);
          setShowRecordingPopup(false);

          // CRITICAL FIX: Don't create any audio blob or URL for recordings over 60 seconds
          // This ensures that no audio URL is created or assigned
          chunks.length = 0; // Clear the chunks array to prevent creating a blob

          return; // Exit early without creating the audio URL
        }

        // If we get here, the recording is valid (under 60 seconds)
        // Reset the recording too long flag
        setIsRecordingTooLong(false);

        // If we get here, the recording is valid (under 60 seconds)
        // Use the same selectedMimeType when creating the Blob
        const audioBlob = new Blob(chunks, { type: selectedMimeType });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Show saving indicator in the input field first (not the save button)
        setIsIntroAudioSaving(true);

        // After a short delay, update with the new audio and clear text
        setTimeout(() => {
          // CRITICAL FIX: Double-check the actual recording duration from the global variable
          // This ensures we don't assign audio URLs for recordings over 60 seconds
          const actualDuration = (window as any)._lastRecordingDuration || 0;

          // Check if the recording is too long - if so, don't create or assign the audio URL
          if (isRecordingTooLong || actualDuration > 60) {

            // End the saving animation
            setIsIntroAudioSaving(false);
            return;
          }

          // Only clear text for this section if we have a valid recording
          if (audioBlob.size > 0) {
            setIntroText('');
          }

          // Set the new audio URL
          setIntroAudioUrl(audioUrl);

          // End the saving animation
          setIsIntroAudioSaving(false);

          // Clear any error state since we have a valid recording
          setRecordingError(null);
          setAudioErrorSection(null);

          // Update change tracking
          setHasIntroChanges(
            initialIntroAudioUrl !== audioUrl ||
            initialIntroText !== '' ||
            initialSelectedIntroPhoto !== selectedIntroPhoto
          );
        }, 1500);

        // Stop all audio tracks
        if (audioStream) {
          audioStream.getTracks().forEach(track => track.stop());
          setAudioStream(null);
        }
      };

      // Set recording state
      setIsRecording(true);

      // Set section-specific recording state - commented out since we now have the animation in the popup
      // if (section === 'intro') {
      //   setIsIntroRecording(true);
      //   setIsOutroRecording(false);
      // } else if (section === 'outro') {
      //   setIsOutroRecording(true);
      //   setIsIntroRecording(false);
      // }

      // Stop any existing timer first
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Start recorder before timer to ensure everything is ready
      recorder.start();

      // IMPORTANT: We need a small delay to ensure React state updates have propagated
      // This is crucial for the timer to work correctly
      setTimeout(() => {
        // Start a new timer
        timerRef.current = setInterval(() => {
          setRecordingTime(prev => {
            return prev + 1;
          });
        }, 1000);
      }, 100);

    } catch (error) {
      console.error('Error accessing microphone:', error);
      alert('Unable to access microphone. Please check your browser permissions.');
    }
  };

  // Stop recording audio
  const stopRecording = () => {
    // Store the current section before clearing it
    const currentSection = recordingFor;

    // Capture the current recording time in a local variable to ensure it doesn't change
    const currentRecordingTime = recordingTime;

    // CRITICAL FIX: Store the current recording time in a global variable
    // that the onstop handler can access, since the state might be reset
    // before the onstop handler runs
    (window as any)._lastRecordingDuration = currentRecordingTime;
    (window as any)._lastRecordingSection = currentSection;

    // Ensure the recorder exists and is active
    if (audioRecorder && audioRecorder.state !== 'inactive') {
      try {
        // Save the current recording time as duration
        setAudioDuration(currentRecordingTime);

        // Also save to the intro section duration
        setIntroAudioDuration(currentRecordingTime);

        // Stop the timer first
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }

        // Stop the recorder - this will trigger the onstop handler we defined in startRecording
        // The onstop handler will check if the recording is too long and handle it appropriately
        audioRecorder.stop();

        // Explicitly stop all tracks in the audio stream to ensure browser recording indicator stops
        if (audioStream) {
          audioStream.getTracks().forEach(track => {
            track.stop();
          });
          setAudioStream(null);
        }

        // Make sure we set the recording state to false
        setIsRecording(false);
        // Commented out since we now have the animation in the popup
        // setIsIntroRecording(false);
        // setIsOutroRecording(false);

        // The saving animation and setting the new audio URL is now handled in the recorder.onstop handler

        // Clear recording section and close popup
        setRecordingFor(null);
        setShowRecordingPopup(false);

      } catch (error) {
        console.error('Error stopping recording:', error);

        // Even if there's an error, try to stop all tracks
        if (audioStream) {
          try {
            audioStream.getTracks().forEach(track => track.stop());
            setAudioStream(null);
          } catch (e) {
            console.error('Error stopping audio tracks after error:', e);
          }
        }
      }
    } else {
      // Clean up anyway
      setIsRecording(false);
      // Commented out since we now have the animation in the popup
      // setIsIntroRecording(false);
      // setIsOutroRecording(false);

      setRecordingFor(null);

      // Close the recording popup
      setShowRecordingPopup(false);

      // Still try to stop any active tracks
      if (audioStream) {
        try {
          audioStream.getTracks().forEach(track => track.stop());
          setAudioStream(null);
        } catch (e) {
          console.error('Error stopping audio tracks in cleanup:', e);
        }
      }

      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  // Play recorded audio with enhanced error handling
  const playRecording = () => {
    // Initialize audio on first interaction
    initializeAudio();
    if (audioContext?.state === 'suspended') {
      audioContext.resume().catch(e => console.error('[playRecording] Error resuming suspended AudioContext:', e));
    }

    const audioUrl = introAudioUrl;
    if (!audioUrl) {
      return;
    }

    // Check if already playing - just stop if that's the case
    if (isPlaying === 'intro') {
      stopPlayback();
      return;
    }

    // Stop any existing playback
    stopPlayback();

    const audio = new Audio();
    audioRef.current = audio; // Store ref immediately

    // Set state before playing
    setIsPlaying('intro');
    setPlaybackTime(0);
    setPlaybackProgress(0);

    // Setup event listeners
    audio.addEventListener('timeupdate', () => {
      if (!audioRef.current) return; // Check ref
      const currentTime = audioRef.current.currentTime;
      const duration = audioRef.current.duration || audioDuration;
      setPlaybackTime(Math.floor(currentTime));
      setPlaybackProgress(Math.min(100, (currentTime / duration) * 100));
    });

    audio.addEventListener('loadedmetadata', () => {
      if (!audioRef.current) return;
      const duration = Math.floor(audioRef.current.duration);
      setAudioDuration(duration);

      // Also update the intro duration
      setIntroAudioDuration(duration);
    });

    audio.addEventListener('ended', () => {
      stopPlayback(); // Use centralized stop function on end
    });

    audio.addEventListener('error', () => {
      stopPlayback(); // Also stop on error
    });

    // Set source
    audio.src = audioUrl;

    // Load the audio
    audio.load();

    // Play immediately
    const playPromise = audio.play();
    if (playPromise !== undefined) {
      playPromise
        .catch(err => {
          console.error('[playRecording] Play promise rejected:', err);
          // Attempting to stop playback cleanly on error
          stopPlayback();
        });
    }
    // If no promise, playback might have started synchronously (older browsers)
    // or failed silently. State should reflect this eventually via events.
  };

  // Stop audio playback with enhanced cleanup
  const stopPlayback = () => {
    // Clean up audio element
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.src = '';
      audioRef.current.load(); // Force cleanup
    }

    // Clean up old audio player if it exists
    if (audioPlayer) {
      audioPlayer.pause();
      audioPlayer.src = '';
    }

    // Reset state
    setIsPlaying(null);
    setPlaybackProgress(0);
    setPlaybackTime(0);
    audioRef.current = null;
  };

  // Handle text input for intro
  const handleTextChange = (value: string) => {
    setIntroText(value);
    // Clear audio when text is set for this section only
    if (value.trim() !== '' && introAudioUrl) {
      URL.revokeObjectURL(introAudioUrl);
      setIntroAudioUrl(null);
    }
    // Update change tracking
    setHasIntroChanges(
      value !== initialIntroText ||
      (introAudioUrl !== initialIntroAudioUrl) ||
      (selectedIntroPhoto !== initialSelectedIntroPhoto)
    );
  };

  // Show save confirmation popup
  const showSaveConfirmationPopup = () => {
    // Check if there's valid content before showing the confirmation
    const hasValidContent = hasValidIntroContent();
    const hasChanges = hasIntroChanges;

    if (!hasValidContent) {
      console.warn(`Cannot save intro without text or audio content`);
      return;
    }

    if (!hasChanges) {
      console.warn(`No changes to save for intro`);
      return;
    }

    setConfirmationSection('intro');
    setShowSaveConfirmation(true);
  };

  // Check if intro has changes
  const checkIntroChanges = (): boolean => {
    // Check text changes
    if (introText !== initialIntroText) return true;

    // Check audio changes (one exists and the other doesn't, or they're different)
    if ((introAudioUrl && !initialIntroAudioUrl) || (!introAudioUrl && initialIntroAudioUrl)) return true;

    // Check photo changes
    const initialPhotoId = initialSelectedIntroPhoto?.photo_id;
    const currentPhotoId = selectedIntroPhoto?.photo_id;
    if ((initialPhotoId && !currentPhotoId) || (!initialPhotoId && currentPhotoId) ||
        (initialPhotoId && currentPhotoId && initialPhotoId !== currentPhotoId)) return true;

    return false;
  };



  // Check if intro has valid content (text or audio)
  const hasValidIntroContent = (): boolean => {
    return (introText.trim() !== '' || introAudioUrl !== null);
  };



  // Reset intro to initial state
  const resetIntroToInitial = () => {
    // First, clear any recording error state
    setRecordingError(null);
    setAudioErrorSection(null);

    // Reset text to initial value
    setIntroText(initialIntroText);

    // If we have a current audio URL that's different from the initial one, revoke it
    if (introAudioUrl && introAudioUrl !== initialIntroAudioUrl) {
      URL.revokeObjectURL(introAudioUrl);
    }

    // Set back to initial audio URL and duration
    setIntroAudioUrl(initialIntroAudioUrl);
    setIntroAudioDuration(initialIntroAudioDuration);
    setSelectedIntroPhoto(initialSelectedIntroPhoto);
    setIntroSearchQuery(''); // Reset search query
    setHasIntroChanges(false);

  };



  // Handle cancel edit
  const handleCancelEdit = () => {
    const hasChanges = checkIntroChanges();

    if (hasChanges) {
      // Show confirmation dialog
      setConfirmationSection('intro');
      setShowCancelConfirmation(true);
    } else {
      // No changes, just exit edit mode
      setIsIntroEditing(false);
    }
  };

  // Confirm cancel with reset
  const confirmCancel = () => {

    // First, stop any playback
    if (isPlaying) {
      stopPlayback();
    }

    // Check if there was a recording that was too long
    // Use both the global flag, the section-specific flags, and the global variables
    const wasRecordingTooLong = isRecordingTooLong ||
                               introRecordingTooLong ||
                               ((window as any)._lastRecordingDuration || 0) > 60 ||
                               (window as any)._introRecordingTooLong === true;

    // Also check section-specific global variables
    const introTooLong = introRecordingTooLong || (window as any)._introRecordingTooLong === true;


    // Reset the appropriate section
    if (confirmationSection === 'intro') {

      // If there was a temporary audio URL created, revoke it
      if (introAudioUrl && introAudioUrl !== initialIntroAudioUrl) {
        URL.revokeObjectURL(introAudioUrl);
      }

      // CRITICAL FIX: If we had a recording that was too long for this section, make sure we restore the initial audio URL
      if ((introTooLong || (audioErrorSection === 'intro' && wasRecordingTooLong))) {
        setIntroAudioUrl(initialIntroAudioUrl);
        setIntroAudioDuration(initialIntroAudioDuration);
        setIntroText(initialIntroText);
      } else {
        // Otherwise, use the normal reset function
        resetIntroToInitial();
      }

      setIsIntroEditing(false);


    // Close any open audio error popup
    setShowAudioErrorPopup(false);

    // Clear any recording error state
    setRecordingError(null);
    setAudioErrorSection(null);

    // Reset the recording too long flags
    setIsRecordingTooLong(false);

    // CRITICAL FIX: Also reset the section-specific recording too long flags
    setIntroRecordingTooLong(false);

    // Clear the global variables
    (window as any)._lastRecordingDuration = 0;
    (window as any)._lastRecordingSection = null;
    (window as any)._introRecordingTooLong = false;
    (window as any)._outroRecordingTooLong = false;

    // Close the confirmation dialog
    setShowCancelConfirmation(false);
    setConfirmationSection(null);
  };

  // Show audio deletion confirmation
  const confirmDeleteAudio = () => {
    setAudioToDelete('intro');
    setShowDeleteAudioConfirmation(true);
  };

  // Delete audio after confirmation
  const deleteAudio = () => {

    if (audioToDelete === 'intro' && introAudioUrl) {

      URL.revokeObjectURL(introAudioUrl);
      setIntroAudioUrl(null);
      setRecordingError(null);
      setIntroText('');

      // Update change tracking
      setHasIntroChanges(
        initialIntroAudioUrl !== null ||
        initialIntroText !== '' ||
        initialSelectedIntroPhoto !== selectedIntroPhoto
      );
    }

    // Close confirmation dialog
    setShowDeleteAudioConfirmation(false);
    setAudioToDelete(null);
  };

  // Function to handle closing the audio error popup
  const handleCloseAudioErrorPopup = () => {

    // When closing the error popup, we want to restore the original content
    // instead of clearing everything

    if (audioErrorSection === 'intro') {

      // CRITICAL FIX: Check if the recording was too long
      const wasRecordingTooLong = isRecordingTooLong || ((window as any)._lastRecordingDuration || 0) > 60;

      // If we're in edit mode, handle differently based on whether the recording was too long
      if (isIntroEditing) {

        // If the recording was too long, we should NOT restore any audio URL
        if (wasRecordingTooLong) {

          // If there's any temporary audio URL, revoke it
          if (introAudioUrl) {
            URL.revokeObjectURL(introAudioUrl);
            setIntroAudioUrl(null); // Explicitly set to null to ensure no audio URL is assigned
          }

          // Restore only the text, not the audio URL
          setIntroText(initialIntroText);
        } else {
          // If the recording was not too long, restore to the initial values

          // If there was a temporary audio URL created, revoke it
          if (introAudioUrl && introAudioUrl !== initialIntroAudioUrl) {
            URL.revokeObjectURL(introAudioUrl);
          }

          // Restore to initial values
          setIntroAudioUrl(initialIntroAudioUrl);
          setIntroText(initialIntroText);
        }
      } else {
        // If not in edit mode, just clear any temporary audio URL
        if (introAudioUrl && !initialIntroAudioUrl) {
          URL.revokeObjectURL(introAudioUrl);
          setIntroAudioUrl(null);
        }
      }
    }

    // Reset recording state
    setIsRecording(false);
    setRecordingFor(null);
    setShowRecordingPopup(false);

    // Reset the recording too long flags
    setIsRecordingTooLong(false);

    // CRITICAL FIX: Also reset the section-specific recording too long flags
    setIntroRecordingTooLong(false);

    // Clear the global variables
    (window as any)._lastRecordingDuration = 0;
    (window as any)._lastRecordingSection = null;
    (window as any)._introRecordingTooLong = false;
    (window as any)._outroRecordingTooLong = false;

    // Clear the error and reset states
    setRecordingError(null);
    setAudioErrorSection(null);

    // Close the popup
    setShowAudioErrorPopup(false);
  };

  // Handle edit button click
  const handleEdit = () => {

    // CRITICAL FIX: Reset recording too long flags when starting a new edit
    setIsRecordingTooLong(false);
    setIntroRecordingTooLong(false);

    // Also clear the global variables
    (window as any)._lastRecordingDuration = 0;
    (window as any)._lastRecordingSection = null;
    (window as any)._introRecordingTooLong = false;
    (window as any)._outroRecordingTooLong = false;

    // Save initial state for tracking changes
    setInitialIntroText(introText);
    setInitialIntroAudioUrl(introAudioUrl);
    setInitialIntroAudioDuration(introAudioDuration);
    // Note: initialIntroAudioFilePath is already set during page load
    setInitialSelectedIntroPhoto(selectedIntroPhoto ? {...selectedIntroPhoto} : null);
    setHasIntroChanges(false);
    setIsIntroEditing(true);
  };

  // Upload audio file to storage
  const uploadAudioToStorage = async (audioUrl: string, section: 'intro' | 'outro', clientId: string): Promise<{ audioFilePath: string, audioDuration: number } | null> => {
    try {
      // Convert the blob URL to a Blob object
      const response = await fetch(audioUrl);
      const audioBlob = await response.blob();

      if (audioBlob.size === 0) {
        console.error('Audio blob is empty');
        return null;
      }

      // Determine file extension & mime type
      const mimeType = audioBlob.type || 'application/octet-stream';
      let fileExtension = 'mp4'; // Default extension
      const mimeParts = mimeType.split('/');
      if (mimeParts.length > 1) {
        const subType = mimeParts[1].split(';')[0]; // Handle potential codecs string
        if (subType === 'mp4') fileExtension = 'mp4';
        else if (subType === 'webm') fileExtension = 'webm';
        else if (subType === 'opus') fileExtension = 'opus';
        else if (subType === 'ogg') fileExtension = 'oga'; // Changed to oga for consistency
        else fileExtension = subType; // Use subtype if known
      }

      // Create a unique file name based on client ID and section
      const chatId = section === 'intro' ? `${clientId}-1` : `${clientId}-2`;
      const uniqueFileName = `${chatId}_${uuidv4()}.${fileExtension}`;

      // Get the authenticated user's ID (auth.id) from the session
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user?.id) {
        throw new Error('User session not found. Please log in again.');
      }
      const userId = session.user.id; // This is the auth UUID

      // Use userId (auth UUID) for the storage path, not clientId
      const filePath = `${userId}/${uniqueFileName}`;
      const bucketName = 'audios';

      // Direct upload to Supabase Storage
      const { error: uploadError } = await supabase
        .storage
        .from(bucketName)
        .upload(filePath, audioBlob, {
          cacheControl: '3600',
          contentType: mimeType,
          upsert: false
        });

      if (uploadError) {
        console.error("Upload error:", uploadError);
        throw new Error(`Failed to upload audio: ${uploadError.message}`);
      }

      // Get Public URL
      const { data: urlData } = supabase
        .storage
        .from(bucketName)
        .getPublicUrl(filePath);

      if (!urlData?.publicUrl) {
        throw new Error(`Could not get public URL for ${filePath}`);
      }

      return {
        audioFilePath: filePath,
        audioDuration: audioDuration || 0
      };
    } catch (error: any) {
      console.error('Error uploading audio:', error);
      // Throw the error so it can be caught and displayed in the UI
      throw new Error(error.message || 'Failed to upload audio file');
    }
  };

  // Save intro settings to welcome_chat table
  const handleSave = async () => {
    // Close the confirmation dialog
    setShowSaveConfirmation(false);

    // Set the appropriate saving state based on the section
    setIsIntroSaving(true);
    setIsIntroEditing(false);

    // Set initial loading state
    setSaveStatus('loading');
    setUpdateProgress(0);
    setUpdateMessage('Preparing to save intro message...');

    try {
      const clientId = clientInfo?.client_id;
      const sector = clientInfo?.sector;
      const lang = clientInfo?.lang;

      if (!clientId) {
        console.error('Client ID not found while saving intro/outro');
        setUpdateMessage('Client ID not found. Please try again or contact support.');
        setSaveStatus('error');
        return;
      }

      // Use the actual chat_id from database
      const chatId = introChatId;

      if (!chatId) {
        console.error('No intro chat_id found. Record may not exist.');
        setUpdateMessage('Intro record not found. Please refresh the page.');
        setSaveStatus('error');
        return;
      }

      // Prepare data for database update
      let updateData: any = {
        client_id: clientId,
        chat_id: chatId
      };

      // Update progress
      setUpdateProgress(20);
      setUpdateMessage('Processing data...');

      // Delete old audio file if it exists and we're replacing it or using text instead
      const existingAudioFilePath = initialIntroAudioFilePath;
      if (existingAudioFilePath) {
        const hasNewAudio = introAudioUrl;
        const hasText = introText;

        // If we have new audio or text, delete the old audio file
        if (hasNewAudio || hasText) {
          try {
            setUpdateMessage('Removing old audio file...');
            const { error: deleteError } = await supabase
              .storage
              .from('audios')
              .remove([existingAudioFilePath]);

            if (deleteError) {
              console.error('Error deleting old audio file:', deleteError);
              // Continue with the save process even if delete fails
            }
          } catch (deleteError) {
            console.error('Error during old audio deletion:', deleteError);
            // Continue with the save process even if delete fails
          }
        }
      }

      // Handle audio upload if exists
      let audioUploadResult = null;
      if (introAudioUrl) {
        setUpdateMessage('Uploading audio file...');
        setUpdateProgress(30);

        try {
          audioUploadResult = await uploadAudioToStorage(introAudioUrl, 'intro', clientId);

          if (!audioUploadResult) {
            throw new Error('Failed to upload audio file');
          }
        } catch (uploadError: any) {
          console.error('Audio upload error:', uploadError);
          setUpdateMessage(uploadError.message || 'Failed to upload audio file');
          setSaveStatus('error');
          throw uploadError; // Re-throw to stop the save process
        }

        setUpdateProgress(60);
        setUpdateMessage('Audio uploaded successfully');
      } else {
        setUpdateProgress(40);
      }

      // Use answer_p field for updates (consistent with other knowledge pages)
      const currentText = introText;
      updateData.answer_p = currentText;

      // Add photo data if selected
      if (selectedIntroPhoto) {
        updateData.photo_url = selectedIntroPhoto.full_photo_urls;
        updateData.photo_id = selectedIntroPhoto.photo_id;
      }

      // Add audio data if uploaded successfully
      if (audioUploadResult) {
        // Get the public URL directly from Supabase
        const { data: urlData } = supabase
          .storage
          .from('audios')
          .getPublicUrl(audioUploadResult.audioFilePath);

        updateData.audio_url = urlData?.publicUrl || '';
        updateData.audio_duration = audioUploadResult.audioDuration;
        updateData.audio_file_path = audioUploadResult.audioFilePath;

        // Clear text when audio exists - use answer_p field
        updateData.answer_p = '';
      } else if (introText.trim() !== '') {
        // If we have text but no audio, save text to answer_p field and clear audio fields
        updateData.answer_p = introText;
        updateData.audio_url = null;
        updateData.audio_duration = null;
        updateData.audio_file_path = null;
      }

      setUpdateProgress(70);
      setUpdateMessage('Saving to database...');

      // Check if record still exists (it might have changed since we checked earlier)
      setUpdateProgress(80);

      // Determine what content has changed to handle ATM ID fields
      const hasAudioChanged = (introAudioUrl !== initialIntroAudioUrl);
      const hasPhotoChanged = (selectedIntroPhoto?.photo_id !== initialSelectedIntroPhoto?.photo_id);

      // Handle ATM ID fields based on what changed
      if (hasAudioChanged && hasPhotoChanged) {
        // Both audio and photo changed: clear all ATM IDs
        updateData.fb_photo_atmid = null;
        updateData.ig_photo_atmid = null;
        updateData.tg_photo_atmid = null;
        updateData.fb_audio_atmid = null;
        updateData.ig_audio_atmid = null;
        updateData.tg_audio_atmid = null;
      } else if (hasAudioChanged) {
        // Only audio changed: clear audio ATM IDs
        updateData.fb_audio_atmid = null;
        updateData.ig_audio_atmid = null;
        updateData.tg_audio_atmid = null;
      } else if (hasPhotoChanged) {
        // Only photo changed: clear photo ATM IDs
        updateData.fb_photo_atmid = null;
        updateData.ig_photo_atmid = null;
        updateData.tg_photo_atmid = null;
      }
      // If only text changed, no ATM ID fields need to be cleared

      // Always update existing record (intro/outro generated during registration)
      setUpdateMessage('Updating record...');
      try {
        const { error } = await supabase
          .from('welcome_chat')
          .update(updateData)
          .eq('client_id', clientId)
          .eq('chat_id', chatId);

        if (error) throw error;
      } catch (error) {
        console.error('Error updating record:', error);
        throw new Error('Failed to update record');
      }

      setUpdateProgress(90);
      setUpdateMessage('Intro message saved successfully!');

      // Trigger webhook for all updates (text, audio, and photo changes)
      // TODO: Temporarily commented out - not sending anything to this webhook currently
      /*
      try {

        // Find the original data to compare changes
        const originalPhoto = section === 'intro' ? initialSelectedIntroPhoto : initialSelectedOutroPhoto;
        const currentPhoto = section === 'intro' ? selectedIntroPhoto : selectedOutroPhoto;

        // Determine if photo has changed
        const hasPhotoChanged =
          // Photo added (didn't exist before but exists now)
          (currentPhoto && !originalPhoto) ||
          // Photo removed (existed before but not now)
          (!currentPhoto && originalPhoto) ||
          // Photo changed (different photo ID)
          (currentPhoto && originalPhoto &&
           originalPhoto.photo_id !== currentPhoto.photo_id);

        // Get current text and audio URL
        const currentText = section === 'intro' ? introText : outroText;
        const audioUrl = section === 'intro' ?
          (updateData.audio_url || "") :
          (updateData.audio_url || "");

        // Prepare webhook data
        const welcomeData = {
          chat_id: chatId,
          client_id: clientId,
          answer: currentText,
          sector,
          lang: lang || 'en', // Send language so webhook knows which fields were updated
          audio_url: audioUrl,
          is_photo: hasPhotoChanged
        };

        // Send webhook for all updates
        await sendWelcomeUpdateWebhook(welcomeData);
        setUpdateProgress(100);
        setUpdateMessage(`${section === 'intro' ? 'Intro' : 'Outro'} message saved!`);
      } catch (webhookError) {
        console.error('Error sending welcome webhook:', webhookError);
        // Don't fail the save operation if webhook fails
        setUpdateProgress(100);
      }
      */

      setUpdateProgress(100);
      setUpdateMessage('Intro message saved!');

      // Set success status
      setSaveStatus('success');

    } catch (error: any) {
      console.error('Error saving intro/outro:', error);
      setUpdateMessage(error.message || 'Error saving intro message. Please try again.');
      setSaveStatus('error');
    } finally {
      // Reset the saving state
      setTimeout(() => {
        setIsIntroSaving(false);

        // Reset save status after a delay
        setTimeout(() => {
          setSaveStatus('idle');
        }, 1500);
      }, 1000);
    }
  };

  // Load existing intro/outro data
  const loadIntroOutroData = async () => {
    try {
      // Wait for dashboard data to load before fetching intro/outro data
      if (!clientInfo?.client_id) {
        return
      }

      const clientId = clientInfo.client_id

      // Fetch all intro/outro data for this client
      const { data: welcomeData, error: welcomeError } = await supabase
        .from('welcome_chat')
        .select('*')
        .eq('client_id', clientId);

      if (welcomeError) {
        console.error('Error fetching welcome data:', welcomeError);
        return;
      }

      // Get intro data based on chat_id pattern
      const introData = welcomeData?.find(record => record.chat_id.endsWith('-1'));

      // Set intro data if exists
      if (introData) {
        // Store the actual chat_id from database
        setIntroChatId(introData.chat_id);

        // Use answer_p field with fallback to answer (consistent with other knowledge pages)
        const introTextValue = introData.answer_p || introData.answer || '';
        setIntroText(introTextValue);
        setInitialIntroText(introTextValue);

        // Set audio URL if exists
        if (introData.audio_url) {
          setIntroAudioUrl(introData.audio_url);
          setInitialIntroAudioUrl(introData.audio_url);
          // Store the audio duration for this specific audio
          const duration = introData.audio_duration || 0;
          setIntroAudioDuration(duration);
          setInitialIntroAudioDuration(duration);
          // Store the audio file path for cleanup
          setInitialIntroAudioFilePath(introData.audio_file_path || null);
        }

        // Set photo if exists
        if (introData.photo_url && introData.photo_id) {
          const photoData = {
            id: 0, // We don't need the actual ID here
            photo_id: introData.photo_id,
            photo_url: Array.isArray(introData.photo_url) && introData.photo_url.length > 0
              ? introData.photo_url[0]
              : null,
            full_photo_urls: introData.photo_url
          };

          setSelectedIntroPhoto(photoData);
          setInitialSelectedIntroPhoto({...photoData});
        }
      }



      // Reset change tracking
      setHasIntroChanges(false);

    } catch (error) {
      console.error('Error loading intro/outro data:', error);
    }
  };

  // Load initial data - only after client info is available
  useEffect(() => {
    if (clientInfo?.client_id) {
      const initializeData = async () => {
        try {
          await Promise.all([
            loadIntroOutroData(),
            fetchAllPhotos() // Fetch all photos on page load
          ]);
        } catch (error) {
          console.error('Error initializing data:', error);
        }
      };

      initializeData();
    }
  }, [clientInfo?.client_id]); // Only run when client info is loaded





  // Effect to initialize audio on first user interaction (if not already initialized)
  useEffect(() => {
    const initAudioOnInteraction = () => {
      if (!audioInitialized) {
        initializeAudio();
        // Remove the listener after the first interaction
        window.removeEventListener('touchstart', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
        window.removeEventListener('mousedown', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
      }
    };

    // Add listeners for the first user interaction
    // Using { once: true } to ensure it only runs once
    if (typeof window !== 'undefined' && !audioInitialized) {
       window.addEventListener('touchstart', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
       window.addEventListener('mousedown', initAudioOnInteraction, { once: true } as AddEventListenerOptions);
    }

    // Cleanup listeners on component unmount
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('touchstart', initAudioOnInteraction);
        window.removeEventListener('mousedown', initAudioOnInteraction);
      }
    };
  }, [audioInitialized]);

  // Memoize the handleSaveEdit function
  const handleSaveEdit = useCallback(() => {
    // Use requestAnimationFrame for smoother UI updates
    requestAnimationFrame(() => {
      if (!editingItem) {
        return;
      }

      // Save the changes
      handleTextChange(editingItem.value);

      // Close modal after state updates
      setEditingItem(null);
      setIsUpdating(true);
      // Simulate saving delay
      setTimeout(() => setIsUpdating(false), 500);
    });
  }, [editingItem]);



  // Effect to auto-focus textarea when editing popup appears
  useEffect(() => {
    // We need to track if this is the initial appearance of the popup
    if (editingItem && textareaRef.current && !hasFocusedInput) {
      // Check if device is desktop (not mobile/tablet) for auto-focus behavior
      const isDesktop = window.innerWidth >= 1024 && !('ontouchstart' in window)

      if (isDesktop) {
        // Use requestAnimationFrame for smoother focusing
        requestAnimationFrame(() => {
          if (textareaRef.current) {
            // Focus the textarea
            textareaRef.current.focus();

            // Place cursor at the end of the text
            const length = textareaRef.current.value.length;
            textareaRef.current.setSelectionRange(length, length);
            setHasFocusedInput(true);
          }
        });
      }
      // On mobile/tablet: let user manually tap to position cursor and handle keyboard
      // Don't set hasFocusedInput to true yet - wait for user's first click
    }
  }, [editingItem, hasFocusedInput]);

  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);

  // Add document visibility change listener
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPlayback();
        // Stop recording when tab becomes inactive to prevent browser recording indicator from staying on
        if (isRecording) {
          stopRecording();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isRecording]);

  // Update change tracking whenever relevant data changes
  useEffect(() => {
    if (isIntroEditing) {
      setHasIntroChanges(checkIntroChanges());
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [introText, introAudioUrl, selectedIntroPhoto, isIntroEditing, initialIntroText, initialIntroAudioUrl, initialSelectedIntroPhoto]);



  // Add debug logging for content validation
  useEffect(() => {
    if (isIntroEditing) {
      const hasContent = hasValidIntroContent();
      const hasChanges = hasIntroChanges;
      console.debug(`Intro - Has content: ${hasContent}, Has changes: ${hasChanges}, Save button enabled: ${hasContent && hasChanges}`);
    }
  }, [introText, introAudioUrl, hasIntroChanges, isIntroEditing]);



  // Cleanup effect to ensure all tracks are stopped when component unmounts
  useEffect(() => {
    return () => {
      // Stop any active recording
      if (isRecording) {
        try {
          if (audioRecorder && audioRecorder.state !== 'inactive') {
            audioRecorder.stop();
          }
        } catch (e) {
          console.error('Error stopping recorder during cleanup:', e);
        }
      }

      // Stop any active audio tracks
      if (audioStream) {
        try {
          audioStream.getTracks().forEach(track => track.stop());
        } catch (e) {
          console.error('Error stopping audio tracks during cleanup:', e);
        }
      }

      // Clear any active timers
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isRecording, audioRecorder, audioStream]);



  // Disable page scroll when gallery is open and add keyboard navigation
  useEffect(() => {
    if (imageGallery) {
      document.body.style.overflow = 'hidden';

      // Add keyboard navigation for photo gallery
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'ArrowLeft') {
          event.preventDefault();
          showPreviousImage();
        } else if (event.key === 'ArrowRight') {
          event.preventDefault();
          showNextImage();
        } else if (event.key === 'Escape') {
          event.preventDefault();
          setImageGallery(null);
        }
      };

      document.addEventListener('keydown', handleKeyDown);

      return () => {
        document.body.style.overflow = '';
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [imageGallery, showPreviousImage, showNextImage]);

  // Disable page scroll when any popup/modal is open
  useEffect(() => {
    const hasAnyPopup = editingItem || showSaveConfirmation || showCancelConfirmation ||
                       saveStatus === 'loading' || saveStatus === 'success' || saveStatus === 'error' ||
                       isRecording || showRecordingPopup || showAudioErrorPopup ||
                       showDeleteAudioConfirmation;

    if (hasAnyPopup) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [editingItem, showSaveConfirmation, showCancelConfirmation, saveStatus,
      isRecording, showRecordingPopup, showAudioErrorPopup, showDeleteAudioConfirmation]);

  return (
    <div className="min-h-screen bg-deep-blue flex flex-col relative pb-16">
      {/* Subtle glow effects for digital dashboard feel */}
      <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white/5 rounded-full blur-[150px] -z-10"></div>
      <div className="absolute bottom-1/3 right-1/3 w-1/3 h-1/3 bg-jade-purple/5 rounded-full blur-[120px] -z-10"></div>

      {/* Modern Glassmorphism Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl px-4 py-3 border border-white/20 hover:border-white/30 transition-all duration-300 overflow-hidden"
          >

            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <Link href="/dashboard" className="group">
                <img
                  src="/images/white_tran_logo.svg"
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 group-hover:scale-105"
                />
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <Link
                href="/dashboard"
                className="inline-flex items-center gap-2 px-3 py-1.5 text-sm text-zinc-300 hover:text-white bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg transition-all duration-200 group"
              >
                <svg
                  className="w-4 h-4 transform -translate-x-0.5 group-hover:-translate-x-1 transition-transform"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                {t('back')}
              </Link>

              <h1 className="text-2xl md:text-3xl font-bold font-title text-jade-purple-dark">
                {t('intro_outro')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* Top Section */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-6">
            {/* Left Card: Infographics */}
            <div
              className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-3 sm:p-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
                style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}>
              <div className="relative z-10">
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title">Knowledge Stats</h2> */}

              {/* Modern Circle Stats Grid */}
              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                {/* Business Insight Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgb(63, 63, 73)"
                        strokeWidth="6"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(129, 82, 228, 0.9)"
                        strokeWidth="6"
                        strokeDasharray="251.2"
                        strokeDashoffset={251.2 * (1 - (totalFaqs / (totalFaqsLimit || 1)))}
                        transform="rotate(-90 50 50)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-black/30 border border-white/20 flex items-center justify-center">
                        <FaBrain className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">{t('brain')}</p>

                  {/* Count */}
                  <p className="text-white text-xs sm:text-base font-body">
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                      </span>
                      : <>{totalFaqs} <span className="text-zinc-400">/ {totalFaqsLimit || 0}</span></>
                    }
                  </p>
                </div>

                {/* Photo Gallery Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgb(63, 63, 73)"
                        strokeWidth="6"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(129, 82, 228, 0.9)"
                        strokeWidth="6"
                        strokeDasharray="251.2"
                        strokeDashoffset={251.2 * (1 - (photoCount / (photoLimit || 1)))}
                        transform="rotate(-90 50 50)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-black/30 border border-white/20 flex items-center justify-center">
                        <FaImage className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className="text-zinc-400 text-[10px] sm:text-xs font-body mb-0 sm:mb-1">{t('photos')}</p>

                  {/* Count */}
                  <p className="text-white text-xs sm:text-base font-body">
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></span>
                      </span>
                      : <>{photoCount} <span className="text-zinc-400">/ {photoLimit || 0}</span></>
                    }
                  </p>
                </div>
              </div>
              </div>
            </div>

            {/* Right Card: Action Buttons */}
            <div
              className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-3 sm:p-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
                style={{
                  boxShadow: '0 0 15px rgba(255, 255, 255, 0.15), inset 0 0 15px rgba(255, 255, 255, 0.15)'
                }}>
              <div className="relative z-10">
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title">Knowledge Management</h2> */}

              {/* Buttons Grid */}
              <div className="grid grid-cols-1 gap-2 sm:gap-3">
                <Link
                  href="/dashboard/knowledge"
                  className="bg-jade-purple/5 hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"

                >
                  {t('business_insight')}
                </Link>
                <Link
                  href="/dashboard/knowledge/photo"
                  className="bg-jade-purple/5 hover:bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"

                >
                  {t('photo_gallery')}
                </Link>
                <Link
                  href="/dashboard/knowledge/intro"
                  className="bg-jade-purple-dark text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center border border-white/20"

                >
                  {t('intro_outro')}
                </Link>
              </div>
              </div>
            </div>
          </div>

          {/* Intro Section */}
          <div
            className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 mb-6 border border-white/20 hover:border-white/40 transition-all duration-300 group overflow-hidden"
          >
            <div className="relative z-10">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center">
                <FaComments className="text-jade-purple mr-3 h-5 w-5" />
                <h2 className="text-xl font-bold font-title">{t('intro_message')}</h2>
              </div>
              <div className="flex space-x-2">
                {isIntroEditing ? (
                  <>
                    <button
                      onClick={() => handleCancelEdit()}
                      className="bg-black/30 text-white hover:bg-black/50 border border-white/20 hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base"
                    >
                      {t('cancel')}
                    </button>
                    <div className="relative">
                      <button
                        onClick={() => showSaveConfirmationPopup()}
                        className={`bg-jade-purple text-white hover:bg-jade-purple-dark border border-jade-purple hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base ${(!hasIntroChanges || !hasValidIntroContent()) ? 'opacity-50 cursor-not-allowed' : ''}`}

                        disabled={isIntroSaving || !hasIntroChanges || !hasValidIntroContent()}
                        title={!hasValidIntroContent() ? "Add text or audio before saving" : (!hasIntroChanges ? "No changes to save" : "Save changes")}
                      >
                        {isIntroSaving ? t('saving') : t('save')}
                      </button>
                      {isIntroEditing && !hasValidIntroContent() && (
                        <div className="absolute -bottom-6 right-0 text-xs text-red-400 whitespace-nowrap">
                          Add text or audio
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <button
                    onClick={() => handleEdit()}
                    className="bg-white/5 hover:bg-white/20 text-white border border-jade-purple hover:shadow-md transition-all duration-200 px-4 py-2 rounded-lg font-body text-xs sm:text-base"

                  >
                    {t('edit')}
                  </button>
                )}
              </div>
            </div>
            <p className="text-white/60 text-sm mb-4 md:mb-6 font-body">
              {t('intro_description')}
            </p>

            {/* Photo Search Bar */}
            <div className="mb-4 relative">
              <div className="relative">
                <input
                  type="text"
                  value={introSearchQuery}
                  onChange={handleIntroPhotoSearch}
                  onFocus={() => introSearchResults.length > 0 && setShowIntroResults(true)}
                  placeholder={t('search_photo_placeholder')}
                  disabled={!isIntroEditing}
                  className={`w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-400 focus:outline-none ${isIntroEditing ? 'focus:border-white/40' : 'cursor-not-allowed opacity-70'}`}
                  style={{
                    fontSize: '16px' // Prevent auto-zoom on mobile
                  }}
                  ref={introSearchInputRef}
                  autoComplete="off" // Prevent browser autocomplete from interfering
                  spellCheck="false" // Disable spell checking
                />
                {isSearchingIntro && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </div>

              {/* Search Results Dropdown */}
              {showIntroResults && (
                <div
                  className="absolute z-50 w-full mt-2 bg-zinc-800/95 border border-white/10 rounded-xl max-h-40 overflow-y-auto backdrop-blur-lg"
                  ref={introSearchResultsRef}
                >
                  {introSearchResults.length > 0 ? (
                    introSearchResults.map(photo => (
                      <div
                        key={photo.id}
                        className="flex items-center gap-3 p-3 hover:bg-white/5 cursor-pointer border-b border-white/5 last:border-0 transition-colors duration-200"
                        onClick={() => handleSelectIntroPhoto(photo)}
                        style={{
                          transition: 'all 0.2s ease'
                        }}
                      >
                        {/* Photo Thumbnail - Optimized */}
                        <PhotoThumbnail
                          photo={photo}
                          className="w-10 h-10"
                        />
                        {/* Photo ID */}
                        <div className="flex-1 truncate">
                          <p className="text-white truncate">{photo.photo_id}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-3 text-zinc-400 text-center">
                      No photos found
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Selected Photo Display or Loading Animation */}
            {isIntroPhotoLoading ? (
              <div className="mb-4 p-4 bg-white/10 border border-white/10 rounded-xl flex items-center justify-center h-16">
                <div className="flex items-center space-x-3">
                  <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-white/70 text-sm">{t('loading')}</span>
                </div>
              </div>
            ) : selectedIntroPhoto && (
              <div className="mb-4 p-3 bg-white/10 border border-white/10 rounded-xl flex items-center justify-between animate-fadeIn">
                <div className="flex items-center gap-3">
                  {/* Photo Thumbnail - Optimized */}
                  <PhotoThumbnail
                    photo={{
                      photo_url: selectedIntroPhoto.full_photo_urls || (selectedIntroPhoto.photo_url ? [selectedIntroPhoto.photo_url] : null),
                      photo_id: selectedIntroPhoto.photo_id
                    }}
                    className="w-10 h-10 border border-white/20"
                    onClick={() => handleViewImage(selectedIntroPhoto.full_photo_urls || (selectedIntroPhoto.photo_url ? [selectedIntroPhoto.photo_url] : null))}
                  />
                  {/* Photo ID */}
                  <div>
                    <p className="text-white">{selectedIntroPhoto.photo_id}</p>
                  </div>
                </div>
                {/* Remove Button */}
                <button
                  disabled={!isIntroEditing}
                  onClick={(e) => isIntroEditing && handleClearSelectedPhoto(e)}
                  className={`p-1 rounded-lg ${isIntroEditing ? 'bg-white/5 hover:bg-white/10 text-zinc-300 hover:text-white border border-white/10' : 'bg-black/30 text-white/30 cursor-not-allowed border border-white/10'} transition-colors duration-200`}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Answer Input */}
            <div className="mb-4">
              <div
                className={`px-2 md:px-4 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-zinc-500 focus:outline-none ${isIntroEditing ? 'hover:border-jade-purple cursor-pointer' : 'opacity-70'} flex items-center min-h-[42px] relative`}

                onClick={() => {
                  // Removed isIntroRecording check since we now have the animation in the popup
                  if (!isIntroEditing || isPlaying === 'intro' || introAudioUrl) {
                    return;
                  }
                  setEditingItem({
                    section: 'intro',
                    value: introText
                  });
                }}
              >
                {/* Regular input display */}
                {/* Removed isIntroRecording check since we now have the animation in the popup */}
                {!introAudioUrl && !isIntroAudioSaving && (
                  introText ?
                    <span className="truncate pr-10">{introText}</span> :
                    <span className="text-zinc-500 truncate">{t('enter_welcome_message')}</span>
                )}

                {/* Recording in progress overlay - commented out since we now have the animation in the popup */}
                {/* {isIntroRecording && (
                  <div className="absolute inset-0 bg-jade-purple/40 border border-jade-purple/70 flex items-center justify-center z-10 rounded-lg" style={{
                    boxShadow: '0 0 15px rgba(134, 107, 255, 0.3)'
                  }}>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
                      <span className="text-white font-mono">{formatRecordingTime(recordingTime)}</span>
                    </div>
                  </div>
                )} */}

                {/* Saving indicator overlay */}
                {isIntroAudioSaving && (
                  <div className="absolute inset-0 border border-white/20 flex items-center justify-center z-20 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-white font-medium">{t('saving')}</span>
                    </div>
                  </div>
                )}

                {/* Audio playback UI */}
                {introAudioUrl && !isIntroAudioSaving && (
                  // <div className={`absolute inset-0 ${isIntroEditing ? 'bg-black/40' : 'bg-black/30'} border border-white/20 flex items-center justify-between px-4 z-10 rounded-lg`} style={{
                  <div className={`absolute inset-0 border border-white/20 flex items-center justify-between px-4 z-10 rounded-lg`}>
                    {/* Progress bar - Full width background */}
                    {isPlaying === 'intro' && (
                      <div className="absolute inset-0 bg-jade-purple/90 z-0 rounded-lg" style={{
                        width: `${playbackProgress}%`,
                        transition: 'width 0.3s linear'
                      }}></div>
                    )}

                    <div className="flex items-center space-x-2 flex-grow z-10">
                      {recordingError ? (
                        <span className="text-red-400 truncate">
                          Error: {recordingError}
                        </span>
                      ) : (
                        <>
                          {!recordingError && isPlaying !== 'intro' && (
                            <button
                              type="button"
                              className="mr-2 p-1 rounded-full bg-jade-purple hover:bg-jade-purple-dark text-white touch-manipulation"
                              style={{ touchAction: "manipulation" }}
                              onClick={() => playRecording()}
                              onTouchEnd={(e) => {
                                e.preventDefault();
                                playRecording();
                              }}
                              aria-label="Play audio"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </button>
                          )}
                          {isPlaying === 'intro' && (
                            <button
                              type="button"
                              className="mr-2 p-1 rounded-full bg-zinc-600 hover:bg-zinc-500 text-white"
                              onClick={stopPlayback}
                              onTouchEnd={(e) => {
                                e.preventDefault();
                                stopPlayback();
                              }}
                              aria-label="Stop audio"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <rect x="6" y="6" width="12" height="12" rx="2" />
                              </svg>
                            </button>
                          )}
                          <span className="text-white truncate">
                            {t('audio')} ({isPlaying === 'intro' ? (audioDuration - playbackTime) : introAudioDuration}s)
                          </span>
                        </>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 z-10">
                      <button
                        type="button"
                        disabled={!isIntroEditing}
                        className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-full ${isIntroEditing ? 'bg-red-500 hover:bg-red-600 text-white' : 'bg-zinc-700 text-zinc-500 cursor-not-allowed'}`}
                        onClick={(e) => {
                          if (!isIntroEditing) return;
                          e.stopPropagation();
                          if (isPlaying === 'intro') {
                            stopPlayback();
                          }
                          // Show confirmation dialog instead of deleting immediately
                          confirmDeleteAudio();
                        }}
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                )}

                {/* Microphone button */}
                <button
                  type="button"
                  disabled={!isIntroEditing}
                  className={`absolute right-2 p-1.5 rounded-full transition-colors ${isIntroEditing ? 'bg-black/40 text-white/70 hover:bg-jade-purple hover:border-jade-purple-dark hover:text-white border border-white/20' : 'bg-black/30 text-white/30 cursor-not-allowed border border-white/10'}`}
                  onClick={(e) => {
                    if (!isIntroEditing) return;
                    e.stopPropagation();
                    e.preventDefault();



                    setShowRecordingPopup(true);
                    setRecordingFor('intro');
                    // Don't start recording here - wait for user to click Start Recording

                    return false;
                  }}
                  title={isIntroEditing ? "Record intro" : "Edit mode required"}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </button>
              </div>
            </div>
            </div>
          </div>
          

          {/* Recording Popup Window */}
          {showRecordingPopup && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-white/20 overflow-hidden"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                <div className="relative z-10">
                {/* Close button (X) */}
                <button
                  className="absolute top-0 right-0 p-1 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors"
                  onClick={() => {
                    // Make sure to stop recording properly to ensure all tracks are stopped
                    if (isRecording) {
                      stopRecording();
                    } else {
                      // Even if not recording, make sure to clean up any active tracks
                      if (audioStream) {
                        audioStream.getTracks().forEach(track => track.stop());
                        setAudioStream(null);
                      }
                      // Clear recording state and close popup
                      setRecordingFor(null);
                      setShowRecordingPopup(false);
                    }
                  }}
                  aria-label="Close"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {recordingFor === 'intro' ? t('record_intro_audio') : t('record_outro_audio')}
                </h3>

                <div className="flex justify-center items-center mb-6 h-32">
                  <div className={`w-32 h-32 rounded-full flex items-center justify-center ${isRecording ? 'bg-red-500/20 border-2 border-red-500 animate-pulse' : 'bg-black/30 border-2 border-white/20'}`}>
                    <div className="text-center">
                      <div className="text-2xl font-mono">
                        {isRecording ? formatRecordingTime(recordingTime) : "0s"}
                      </div>
                      <div className="text-sm text-white/60 mt-1">
                        {isRecording ? t('recording') : t('ready')}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-center">
                  {!isRecording ? (
                    <button
                      onClick={() => {
                        startRecording();
                      }}
                      className="py-2 px-4 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors flex items-center justify-center border-2 border-jade-purple"

                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                      </svg>
                      {t('start_recording')}
                    </button>
                  ) : (
                    <button
                      onClick={() => {
                        stopRecording();
                      }}
                      className="py-2 px-4 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center justify-center border-2 border-red-500"

                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                      </svg>
                      {t('stop_recording')}
                    </button>
                  )}
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Text Editing Popup */}
          {editingItem && (
            <div
              className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                ref={modalRef}
                className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-white/20 overflow-hidden"
                onClick={(e: React.MouseEvent) => e.stopPropagation()} // Prevent clicks inside the modal from triggering the backdrop click
              >
                <div className="relative z-10">
                {/* Close button (X) */}
                <button
                  className="absolute top-0 right-0 p-1 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors"
                  onClick={() => setEditingItem(null)}
                  aria-label="Close"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {editingItem.section === 'intro' ? t('edit_intro_message') : t('edit_outro_message')}
                </h3>

                <div className="mb-6">
                  <textarea
                    ref={textareaRef}
                    value={editingItem.value}
                    onChange={(e) => {
                      setEditingItem({
                        ...editingItem,
                        value: e.target.value
                      });
                    }}
                    onClick={() => {
                      // On mobile: only position cursor at end on FIRST click (initial focus)
                      // After that, allow free cursor movement
                      if (!hasFocusedInput && textareaRef.current) {
                        const length = textareaRef.current.value.length;
                        textareaRef.current.setSelectionRange(length, length);
                        setHasFocusedInput(true);
                      }
                      // Subsequent clicks: let user position cursor freely (default browser behavior)
                    }}
                    placeholder={editingItem.section === 'intro' ? t('enter_welcome_message') : t('enter_closing_message')}
                    className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:border-white/40 min-h-[120px]"

                  />
                </div>

                <button
                  onClick={handleSaveEdit}
                  className="py-2 px-4 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors w-full border-2 border-jade-purple"

                >
                  {t('done')}
                </button>
                </div>
              </div>
            </div>
          )}

          {/* Save Confirmation Modal */}
          {showSaveConfirmation && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-white/20 overflow-hidden"
                onClick={(e: React.MouseEvent) => e.stopPropagation()} // Prevent clicks inside the modal from closing it
              >
                <div className="relative z-10">
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {confirmationSection === 'intro' ? t('save_intro_message') : t('save_outro_message')}
                </h3>

                <p className="text-white/80 mb-6 text-center">
                  {confirmationSection === 'intro' ? t('save_intro_confirmation') : t('save_outro_confirmation')}
                </p>

                <div className="flex justify-between w-full space-x-4">
                  <button
                    onClick={() => setShowSaveConfirmation(false)}
                    className="flex-1 px-4 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 text-white rounded-lg transition-colors border border-white/20"

                    disabled={saveStatus !== 'idle'}
                  >
                    {t('cancel')}
                  </button>
                  <button
                    onClick={() => {
                      handleSave();
                    }}
                    className="flex-1 px-4 py-2 bg-jade-purple hover:bg-jade-purple-dark text-white rounded-lg transition-colors border-2 border-jade-purple"

                    disabled={saveStatus !== 'idle'}
                  >
                    {t('save')}
                  </button>
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Cancel Confirmation Modal */}
          {showCancelConfirmation && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                className="relative bg-red-500/[0.1] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-red-500/30 overflow-hidden"
                onClick={(e: React.MouseEvent) => e.stopPropagation()} // Prevent clicks inside the modal from closing it
              >
                <div className="relative z-10">
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {t('discard_changes')}
                </h3>

                <p className="text-white/80 mb-6 text-center">
                  {confirmationSection === 'intro' ? t('unsaved_intro_changes') : t('unsaved_outro_changes')}
                </p>

                <div className="flex justify-between w-full space-x-4">
                  <button
                    onClick={() => setShowCancelConfirmation(false)}
                    className="flex-1 px-4 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 text-white rounded-lg transition-colors border border-white/20"

                  >
                    {t('keep_editing')}
                  </button>
                  <button
                    onClick={confirmCancel}
                    className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors border-2 border-red-600"

                  >
                    {t('discard')}
                  </button>
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Audio Delete Confirmation Modal */}
          {showDeleteAudioConfirmation && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                className="relative bg-red-500/[0.1] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-red-500/30 overflow-hidden"
                onClick={(e: React.MouseEvent) => e.stopPropagation()} // Prevent clicks inside the modal from closing it
              >
                <div className="relative z-10">
                <h3 className="text-xl font-bold mb-4 font-title text-center">
                  {t('delete_audio')}
                </h3>

                <p className="text-white/80 mb-6 text-center">
                  {audioToDelete === 'intro' ? t('delete_intro_audio_confirmation') : t('delete_outro_audio_confirmation')}
                </p>

                <div className="flex justify-between w-full space-x-4">
                  <button
                    onClick={() => setShowDeleteAudioConfirmation(false)}
                    className="flex-1 px-4 py-2 bg-black/30 hover:bg-black/50 hover:border-white/50 border border-white/20 text-white rounded-lg transition-colors"

                  >
                    {t('cancel')}
                  </button>
                  <button
                    onClick={deleteAudio}
                    className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors border-2 border-red-600"

                  >
                    {t('delete')}
                  </button>
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Audio Error Popup - similar to the one in Knowledge page */}
          {showAudioErrorPopup && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                className="relative bg-red-500/[0.1] backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border border-red-500/30 overflow-hidden"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                <div className="relative z-10">
                {/* Close button */}
                <button
                  onClick={handleCloseAudioErrorPopup}
                  className="absolute top-0 right-0 p-1 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors"
                  aria-label="Close"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                {/* Error icon */}
                <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full bg-red-500/20">
                  <FaExclamationTriangle className="w-6 h-6 text-red-500" />
                </div>

                {/* Error title */}
                <h3 className="text-xl font-bold mb-4 font-title text-center text-white">
                  Audio Recording Error
                </h3>

                {/* Error message */}
                <p className="text-white/80 mb-6 text-center">
                  Audio recordings are only allowed to be under 60 seconds in length.
                </p>
                </div>
              </div>
            </div>
          )}

          {/* Save Status Overlay - Separate from confirmation modal */}
          {saveStatus !== 'idle' && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                className={`p-6 rounded-xl max-w-md w-full mx-4 ${
                  saveStatus === 'loading' ? 'bg-jade-purple/30 border border-jade-purple/50 text-white' :
                  saveStatus === 'success' ? 'bg-green-500/20 border border-green-500/30 text-green-400' :
                  'bg-red-500/20 border border-red-500/30 text-red-400'
                } backdrop-blur-md`}
                onClick={(e) => e.stopPropagation()}
              >
                {saveStatus === 'loading' ? (
                  <div className="flex flex-col items-center text-center">
                    <div className="w-10 h-10 mb-4 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                    <p className="text-lg font-semibold mb-3">Saving {confirmationSection === 'intro' ? 'Intro' : 'Outro'} Message...</p>
                    <div className="w-full bg-black/30 rounded-full h-3 mb-1">
                      <div
                        className="bg-white h-3 rounded-full transition-all duration-300"
                        style={{ width: `${updateProgress}%` }}
                      ></div>
                    </div>
                    <p className="text-sm">{updateProgress}% complete</p>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className={saveStatus === 'success' ? 'text-green-400' : 'text-red-400'}>
                      {saveStatus === 'success' ? (
                        <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      )}
                    </div>
                    <p className="text-lg font-semibold mb-1">{saveStatus === 'success' ? 'Success!' : 'Error'}</p>
                    <p>{updateMessage}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Image Gallery Modal */}
          {imageGallery && (
            <div
              className="fixed inset-0 bg-black bg-opacity-80 backdrop-blur-sm flex items-center justify-center z-50"
            >
              <div
                ref={imageGalleryRef}
                className="relative bg-white/[0.05] backdrop-blur-xl rounded-2xl p-6 w-full max-w-3xl mx-4 border border-white/20 overflow-hidden"
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                <div className="relative z-10">
                {/* Close button in the top-right corner */}
                <button
                  onClick={() => setImageGallery(null)}
                  className="absolute top-0 right-0 p-1.5 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors z-20"
                  aria-label="Close"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                <div className="flex flex-col">
                  {/* Image counter */}
                  <div className="text-center mb-2 text-sm text-white/60">
                    {imageGallery.currentIndex + 1} / {imageGallery.urls.length}
                  </div>

                  {/* Main image container with touch events */}
                  <div
                    className="w-full flex items-center justify-center h-[60vh]"
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={handleTouchEnd}
                    aria-live="polite"
                    role="region"
                    aria-label={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
                  >
                    {imageGallery.urls.length === 0 ? (
                      <div className="flex flex-col items-center justify-center text-white/50">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p>No images available</p>
                      </div>
                    ) : (
                      <div className="relative">
                        <img
                          src={optimizeGalleryImage(imageGallery.urls[imageGallery.currentIndex])}
                          alt={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
                          className="rounded-lg max-w-full max-h-full object-contain"
                          loading="eager"
                          decoding="async"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/placeholder-image.svg';
                          }}
                        />
                      </div>
                    )}

                {/* Navigation buttons - only show if more than one image */}
                {imageGallery.urls.length > 1 && (
                  <>
                    {/* Previous button */}
                    <button
                      onClick={showPreviousImage}
                      className="absolute left-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Previous image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>

                    {/* Next button */}
                    <button
                      onClick={showNextImage}
                      className="absolute right-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Next image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </>
                )}
                  </div>

                  {/* Thumbnail strip - only show if more than one image */}
                  {imageGallery.urls.length > 1 && (
                    <div className="flex justify-center space-x-2 mt-4 overflow-x-auto py-2">
                      {imageGallery.urls.map((url, index) => (
                        <PhotoThumbnail
                          key={index}
                          photo={{
                            photo_url: [url],
                            photo_id: `thumbnail-${index + 1}`
                          }}
                          className={`w-16 h-16 rounded-lg border-2 transition-all duration-200 ease-in-out ${
                            index === imageGallery.currentIndex ? 'border-white/80 scale-105 opacity-100' : 'border-zinc-700 opacity-70 hover:opacity-90 hover:border-zinc-600'
                          }`}
                          onClick={() => {
                            setImageGallery({
                              ...imageGallery,
                              currentIndex: index
                            });
                          }}
                        />
                      ))}
                    </div>
                  )}
                </div>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </div>
      <Footer />
    </div>
  );
}
