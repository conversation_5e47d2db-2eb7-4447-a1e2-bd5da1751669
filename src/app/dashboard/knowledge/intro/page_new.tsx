'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useClientInfo } from '@/hooks/useClientInfo';
import { useTranslation } from '@/hooks/useTranslation';
import { PhotoGallery } from '@/components/PhotoGallery';
import { ConfirmationModal } from '@/components/ConfirmationModal';
import { LoadingModal } from '@/components/LoadingModal';
import { AudioErrorPopup } from '@/components/AudioErrorPopup';
import { DeleteConfirmationModal } from '@/components/DeleteConfirmationModal';
import { EditTextModal } from '@/components/EditTextModal';

interface Photo {
  photo_id: string;
  full_photo_urls: string;
}

interface IntroData {
  chat_id: string;
  answer_p: string;
  answer: string;
  audio_url: string | null;
  audio_duration: number | null;
  audio_file_path: string | null;
  photo_url: string | null;
  photo_id: string | null;
}

export default function IntroPage() {
  const router = useRouter();
  const { clientInfo } = useClientInfo();
  const { t } = useTranslation();

  // Intro state
  const [introText, setIntroText] = useState('');
  const [introAudioUrl, setIntroAudioUrl] = useState<string | null>(null);
  const [introAudioDuration, setIntroAudioDuration] = useState(0);
  const [selectedIntroPhoto, setSelectedIntroPhoto] = useState<Photo | null>(null);
  const [isIntroEditing, setIsIntroEditing] = useState(false);
  const [isIntroSaving, setIsIntroSaving] = useState(false);
  const [hasIntroChanges, setHasIntroChanges] = useState(false);

  // Initial state tracking for intro
  const [initialIntroText, setInitialIntroText] = useState('');
  const [initialIntroAudioUrl, setInitialIntroAudioUrl] = useState<string | null>(null);
  const [initialIntroAudioDuration, setInitialIntroAudioDuration] = useState(0);
  const [initialIntroAudioFilePath, setInitialIntroAudioFilePath] = useState<string | null>(null);
  const [initialSelectedIntroPhoto, setInitialSelectedIntroPhoto] = useState<Photo | null>(null);

  // Chat ID for database operations
  const [introChatId, setIntroChatId] = useState<string | null>(null);

  // Recording state
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);
  const [recordingFor, setRecordingFor] = useState<'intro' | null>(null);
  const [introRecordingTooLong, setIntroRecordingTooLong] = useState(false);

  // Audio playback state
  const [isPlaying, setIsPlaying] = useState<'intro' | null>(null);
  const [playbackTime, setPlaybackTime] = useState(0);
  const [playbackProgress, setPlaybackProgress] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null);

  // UI state
  const [showPhotoGallery, setShowPhotoGallery] = useState(false);
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const [confirmationSection, setConfirmationSection] = useState<'intro' | null>(null);
  const [showDeleteAudioConfirmation, setShowDeleteAudioConfirmation] = useState(false);
  const [audioToDelete, setAudioToDelete] = useState<'intro' | null>(null);

  // Loading and error state
  const [isLoading, setIsLoading] = useState(true);
  const [updateMessage, setUpdateMessage] = useState('');
  const [updateProgress, setUpdateProgress] = useState(0);
  const [showAudioErrorPopup, setShowAudioErrorPopup] = useState(false);
  const [recordingError, setRecordingError] = useState<string | null>(null);
  const [audioErrorSection, setAudioErrorSection] = useState<'intro' | null>(null);
  const [isRecordingTooLong, setIsRecordingTooLong] = useState(false);
  const [isIntroAudioSaving, setIsIntroAudioSaving] = useState(false);

  // Edit modal state
  const [editingItem, setEditingItem] = useState<{
    section: 'intro';
    value: string;
  } | null>(null);

  // Photo search state
  const [introSearchQuery, setIntroSearchQuery] = useState('');

  // Initialize audio context
  const initializeAudio = () => {
    if (!audioContext) {
      const ctx = new (window.AudioContext || (window as any).webkitAudioContext)();
      setAudioContext(ctx);
    }
  };

  // Format time helper
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Stop audio playback
  const stopPlayback = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current = null;
    }
    setIsPlaying(null);
    setPlaybackTime(0);
    setPlaybackProgress(0);
  };

  // Start recording
  const startRecording = async () => {
    try {
      // Reset any previous recording state
      setIsRecordingTooLong(false);
      setIntroRecordingTooLong(false);
      setRecordingError(null);
      setAudioErrorSection(null);
      setShowAudioErrorPopup(false);

      // Clear any global variables
      (window as any)._lastRecordingDuration = 0;
      (window as any)._introRecordingTooLong = false;

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setAudioStream(stream);

      const recorder = new MediaRecorder(stream);
      setMediaRecorder(recorder);

      const audioChunks: Blob[] = [];

      recorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      recorder.onstop = () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Store the current recording time for intro section
        const currentRecordingTime = recordingTime;
        setIntroAudioDuration(currentRecordingTime);

        // Check if recording was too long (over 60 seconds)
        if (currentRecordingTime > 60) {
          // Store in global variables for persistence
          (window as any)._lastRecordingDuration = currentRecordingTime;
          (window as any)._introRecordingTooLong = true;

          setIntroRecordingTooLong(true);
          setRecordingError("Over 60s");
          setAudioErrorSection('intro');
          setShowAudioErrorPopup(true);

          // Stop all audio tracks
          if (audioStream) {
            audioStream.getTracks().forEach(track => track.stop());
            setAudioStream(null);
          }

          setIsIntroAudioSaving(false);
          setIsRecording(false);
          setRecordingFor(null);
          return;
        }

        // If recording is valid, set the audio URL
        setIntroAudioUrl(audioUrl);

        // Clear any existing text when audio is recorded
        if (introText.trim() !== '') {
          setIntroText('');
        }

        // Update change tracking
        setHasIntroChanges(
          audioUrl !== initialIntroAudioUrl ||
          '' !== initialIntroText ||
          selectedIntroPhoto !== initialSelectedIntroPhoto
        );

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
        setAudioStream(null);
      };

      setIsRecording(true);
      setRecordingTime(0);
      setRecordingFor('intro');
      recorder.start();

      // Start timer
      const timer = setInterval(() => {
        setRecordingTime(prev => {
          const newTime = prev + 1;
          
          // Check if we've exceeded 60 seconds
          if (newTime > 60) {
            clearInterval(timer);
            
            // Stop recording immediately
            if (recorder.state === 'recording') {
              recorder.stop();
            }
            
            // Store the duration globally
            (window as any)._lastRecordingDuration = newTime;
            (window as any)._introRecordingTooLong = true;

            setIntroRecordingTooLong(true);
            setRecordingError("Over 60s");
            setAudioErrorSection('intro');
            setShowAudioErrorPopup(true);
            setIsRecording(false);
            setRecordingTime(0);
            setRecordingFor(null);
            setIsIntroAudioSaving(false);

            // Stop all audio tracks
            if (audioStream) {
              audioStream.getTracks().forEach(track => track.stop());
              setAudioStream(null);
            }

            return newTime;
          }
          
          return newTime;
        });
      }, 1000);

      // Store timer reference for cleanup
      (window as any)._recordingTimer = timer;

    } catch (error) {
      console.error('Error starting recording:', error);
      setRecordingError('Microphone access denied');
      setShowAudioErrorPopup(true);
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
    }
    
    // Clear timer
    if ((window as any)._recordingTimer) {
      clearInterval((window as any)._recordingTimer);
      (window as any)._recordingTimer = null;
    }
    
    setIsRecording(false);
    setRecordingFor(null);
  };

  // Play recorded audio
  const playRecording = () => {
    initializeAudio();
    if (audioContext?.state === 'suspended') {
      audioContext.resume().catch(e => console.error('[playRecording] Error resuming suspended AudioContext:', e));
    }

    const audioUrl = introAudioUrl;
    if (!audioUrl) {
      return;
    }

    // Check if already playing - just stop if that's the case
    if (isPlaying === 'intro') {
      stopPlayback();
      return;
    }

    // Stop any existing playback
    stopPlayback();

    const audio = new Audio();
    audioRef.current = audio;

    // Set state before playing
    setIsPlaying('intro');
    setPlaybackTime(0);
    setPlaybackProgress(0);

    // Setup event listeners
    audio.addEventListener('timeupdate', () => {
      if (!audioRef.current) return;
      const currentTime = audioRef.current.currentTime;
      const duration = audioRef.current.duration || audioDuration;
      setPlaybackTime(Math.floor(currentTime));
      setPlaybackProgress(Math.min(100, (currentTime / duration) * 100));
    });

    audio.addEventListener('loadedmetadata', () => {
      if (!audioRef.current) return;
      const duration = Math.floor(audioRef.current.duration);
      setAudioDuration(duration);
      setIntroAudioDuration(duration);
    });

    audio.addEventListener('ended', () => {
      stopPlayback();
    });

    audio.addEventListener('error', () => {
      stopPlayback();
    });

    // Set source and play
    audio.src = audioUrl;
    audio.load();

    const playPromise = audio.play();
    if (playPromise !== undefined) {
      playPromise.catch(err => {
        console.error('[playRecording] Play promise rejected:', err);
        stopPlayback();
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Content will be added in next edit */}
    </div>
  );
}
